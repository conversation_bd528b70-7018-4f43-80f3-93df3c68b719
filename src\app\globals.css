@import "tailwindcss";
@import "../styles/design-system.css";

:root {
  /* iOS风格颜色变量 */
  --background: #ffffff;
  --foreground: #000000;
  --surface-light: #ffffff;
  --surface-gray: #f2f2f7;
  --surface-dark: #1c1c1e;
  --text-primary: #000000;
  --text-secondary: #3c3c43;
  --text-tertiary: rgba(60, 60, 67, 0.6);
  --text-quaternary: rgba(60, 60, 67, 0.18);
  --ios-blue: #007AFF;
  --ios-green: #34C759;
  --ios-red: #FF3B30;
  --ios-orange: #FF9500;
  --ios-yellow: #FFCC00;
  --ios-purple: #AF52DE;
  --ios-pink: #FF2D92;
  --ios-teal: #5AC8FA;
  --ios-indigo: #5856D6;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
    --surface-light: #1c1c1e;
    --surface-gray: #2c2c2e;
    --surface-dark: #000000;
    --text-primary: #ffffff;
    --text-secondary: rgba(235, 235, 245, 0.6);
    --text-tertiary: rgba(235, 235, 245, 0.3);
    --text-quaternary: rgba(235, 235, 245, 0.16);
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', 'Roboto', sans-serif;
  background: var(--background);
  color: var(--foreground);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* iOS风格滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* iOS风格选择高亮 */
::selection {
  background: var(--ios-blue);
  color: white;
}

/* iOS风格焦点样式 */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--ios-blue);
  outline-offset: 2px;
}

/* iOS风格按钮点击效果 */
.ios-button-effect {
  transition: all 0.1s ease;
}

.ios-button-effect:active {
  transform: scale(0.96);
}

/* iOS风格卡片阴影 */
.ios-card {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.ios-card-lg {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* iOS风格毛玻璃效果 */
.ios-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* iOS风格动画 */
.ios-transition {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  html {
    font-size: 15px;
  }
}

@media (min-width: 1025px) {
  html {
    font-size: 16px;
  }
}
