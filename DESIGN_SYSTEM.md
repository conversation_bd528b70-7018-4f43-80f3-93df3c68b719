# 设计系统规范

## 📐 布局结构严谨性

### 容器系统
- **container-main**: 主要内容容器，最大宽度 7xl，响应式内边距
- **container-content**: 内容容器，最大宽度 4xl
- **container-narrow**: 窄容器，最大宽度 2xl

### 网格系统
- **grid-main**: 12列主网格系统
- **grid-sidebar**: 侧边栏区域 (3列)
- **grid-content**: 内容区域 (6列)
- **grid-aside**: 辅助区域 (3列)
- **grid-two-col**: 两列网格
- **grid-three-col**: 三列网格
- **grid-four-col**: 四列网格

## 📏 间距系统统一

### 标准间距
- **spacing-section**: 页面区块间距 (py-8 md:py-12 lg:py-16)
- **spacing-component**: 组件内边距 (p-4 md:p-6 lg:p-8)
- **spacing-card**: 卡片内边距 (p-4 md:p-6)
- **spacing-list-item**: 列表项间距 (py-3 px-4)
- **spacing-form**: 表单元素间距 (space-y-4 md:space-y-6)
- **spacing-button-group**: 按钮组间距 (space-x-2 md:space-x-3)
- **spacing-stack**: 垂直堆叠间距 (space-y-3 md:space-y-4)
- **spacing-grid**: 网格间距 (gap-4 md:gap-6 lg:gap-8)

## 🎨 配色系统统一

### iOS 系统颜色
```css
--ios-blue: #007AFF     /* 主要操作 */
--ios-green: #34C759    /* 成功状态 */
--ios-red: #FF3B30      /* 错误/危险 */
--ios-orange: #FF9500   /* 警告状态 */
--ios-yellow: #FFCC00   /* 提示信息 */
--ios-purple: #AF52DE   /* 特殊标记 */
--ios-pink: #FF2D92     /* 强调内容 */
--ios-teal: #5AC8FA     /* 辅助信息 */
--ios-indigo: #5856D6   /* 次要操作 */
```

### 表面颜色
```css
--surface-light: #ffffff    /* 主要背景 */
--surface-gray: #f2f2f7     /* 次要背景 */
--surface-dark: #1c1c1e     /* 深色背景 */
```

### 文本颜色层次
```css
--text-primary: #000000           /* 主要文本 */
--text-secondary: #3c3c43         /* 次要文本 */
--text-tertiary: rgba(60,60,67,0.6)   /* 辅助文本 */
--text-quaternary: rgba(60,60,67,0.18) /* 占位文本 */
```

## 📱 响应式布局规范

### 断点系统
- **mobile**: < 768px
- **tablet**: 768px - 1024px
- **desktop**: > 1024px

### 响应式工具类
- **mobile-only**: 仅移动端显示
- **desktop-only**: 仅桌面端显示
- **tablet-up**: 平板及以上显示
- **mobile-tablet**: 移动端和平板显示

### 布局适配
- **移动端**: 单栏布局 + 底部导航
- **平板端**: 两栏布局 + 侧边栏
- **桌面端**: 三栏布局 + 完整侧边栏

## 🧩 组件布局规范

### 卡片组件
- **card-base**: 基础卡片样式
- **card-header**: 卡片头部 (px-6 py-4 + 底部边框)
- **card-content**: 卡片内容 (px-6 py-4)
- **card-footer**: 卡片底部 (px-6 py-4 + 顶部边框)
- **card-compact**: 紧凑卡片 (p-4)
- **card-spacious**: 宽松卡片 (p-6 md:p-8)

### 列表组件
- **list-container**: 列表容器 (space-y-1)
- **list-item**: 标准列表项 (py-3 px-4)
- **list-item-compact**: 紧凑列表项 (py-2 px-3)
- **list-item-spacious**: 宽松列表项 (py-4 px-6)
- **list-divider**: 列表分隔线

### 导航组件
- **nav-container**: 导航容器 (h-16 + 水平内边距)
- **nav-brand**: 品牌区域
- **nav-menu**: 导航菜单 (桌面端)
- **nav-actions**: 操作按钮区域

### 侧边栏组件
- **sidebar-container**: 侧边栏容器 (w-64)
- **sidebar-header**: 侧边栏头部 (h-16)
- **sidebar-content**: 侧边栏内容区
- **sidebar-footer**: 侧边栏底部
- **sidebar-nav**: 导航菜单容器
- **sidebar-nav-item**: 导航项基础样式
- **sidebar-nav-item-active**: 激活状态
- **sidebar-nav-item-inactive**: 非激活状态

### 模态框组件
- **modal-overlay**: 模态框遮罩层
- **modal-container**: 模态框容器
- **modal-header**: 模态框头部
- **modal-content**: 模态框内容
- **modal-footer**: 模态框底部

### 表单组件
- **form-container**: 表单容器 (space-y-6)
- **form-group**: 表单组 (space-y-2)
- **form-row**: 表单行 (网格布局)
- **form-actions**: 表单操作区

## 📝 文本层次规范

### 标题层次
- **text-display**: 展示标题 (4xl-6xl)
- **text-headline**: 页面标题 (2xl-4xl)
- **text-title**: 区块标题 (xl-2xl)
- **text-subtitle**: 副标题 (lg-xl)
- **text-body**: 正文 (base)
- **text-caption**: 说明文字 (sm)
- **text-footnote**: 脚注 (xs)

### 内容区域
- **content-header**: 内容头部
- **content-title**: 内容标题
- **content-subtitle**: 内容副标题
- **content-actions**: 内容操作区
- **content-body**: 内容主体

## 🎭 交互状态规范

### 基础交互
- **interactive-base**: 基础过渡效果
- **interactive-hover**: 悬停效果
- **interactive-press**: 按压效果
- **interactive-focus**: 焦点效果

### 状态指示
- **status-online**: 在线状态 (绿色)
- **status-offline**: 离线状态 (灰色)
- **status-warning**: 警告状态 (橙色)
- **status-error**: 错误状态 (红色)

### 优先级指示
- **priority-high**: 高优先级 (红色)
- **priority-normal**: 普通优先级 (灰色)
- **priority-low**: 低优先级 (浅灰)

## 🔄 加载和状态

### 加载状态
- **loading-container**: 加载容器
- **loading-overlay**: 加载遮罩
- **skeleton-base**: 骨架屏基础
- **skeleton-text**: 文本骨架
- **skeleton-avatar**: 头像骨架

### 空状态
- **empty-container**: 空状态容器
- **empty-icon**: 空状态图标
- **empty-title**: 空状态标题
- **empty-message**: 空状态描述

### 错误状态
- **error-container**: 错误容器
- **error-icon**: 错误图标
- **error-title**: 错误标题
- **error-message**: 错误描述

## 🎯 使用原则

### 1. 一致性原则
- 所有组件必须使用统一的间距系统
- 颜色使用必须遵循定义的色彩规范
- 文本层次必须保持一致

### 2. 响应式原则
- 优先考虑移动端体验
- 确保在所有设备上的可用性
- 合理使用响应式工具类

### 3. 可访问性原则
- 保持足够的颜色对比度
- 提供清晰的焦点指示
- 支持键盘导航

### 4. 性能原则
- 避免过度的动画效果
- 合理使用CSS类组合
- 优化加载状态展示

## 📋 检查清单

在实现新组件时，请确保：

- [ ] 使用了正确的容器类
- [ ] 应用了统一的间距系统
- [ ] 遵循了颜色规范
- [ ] 实现了响应式布局
- [ ] 添加了适当的交互状态
- [ ] 考虑了加载和错误状态
- [ ] 保持了文本层次的一致性
- [ ] 符合可访问性要求

---

**记住**: 设计系统的目标是创建一致、直观、高效的用户界面。每个决定都应该服务于更好的用户体验。
