'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Star, 
  Paperclip, 
  Archive, 
  Trash2, 
  MoreHorizontal,
  CheckCircle2,
  Circle
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Avatar } from '@/components/ui/Avatar';
import { formatRelativeTime, truncateText, cn } from '@/lib/utils';


interface Email {
  id: string;
  from: {
    name: string;
    email: string;
    avatar?: string;
  };
  subject: string;
  preview: string;
  timestamp: Date;
  isRead: boolean;
  isStarred: boolean;
  hasAttachment: boolean;
  isSelected: boolean;
  priority?: 'high' | 'normal' | 'low';
  labels?: string[];
}

interface EmailListProps {
  emails: Email[];
  onEmailClick?: (email: Email) => void;
  onEmailSelect?: (emailId: string, selected: boolean) => void;
  onStarToggle?: (emailId: string) => void;
  onArchive?: (emailId: string) => void;
  onDelete?: (emailId: string) => void;
  className?: string;
}

const mockEmails: Email[] = [
  {
    id: '1',
    from: {
      name: 'GitHub',
      email: '<EMAIL>',
      avatar: '/api/placeholder/32/32'
    },
    subject: '[GitHub] 您的账户安全提醒',
    preview: '我们检测到您的账户有新的登录活动，请确认这是您本人的操作...',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
    isRead: false,
    isStarred: true,
    hasAttachment: false,
    isSelected: false,
    priority: 'high',
    labels: ['安全', '重要']
  },
  {
    id: '2',
    from: {
      name: 'Vercel',
      email: '<EMAIL>',
      avatar: '/api/placeholder/32/32'
    },
    subject: '部署成功通知',
    preview: '您的项目已成功部署到生产环境，访问链接：https://your-app.vercel.app',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
    isRead: true,
    isStarred: false,
    hasAttachment: true,
    isSelected: false,
    priority: 'normal'
  },
  {
    id: '3',
    from: {
      name: 'OpenAI',
      email: '<EMAIL>',
      avatar: '/api/placeholder/32/32'
    },
    subject: 'API 使用量报告',
    preview: '您本月的 API 使用量已达到 80%，建议您关注使用情况...',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
    isRead: true,
    isStarred: false,
    hasAttachment: false,
    isSelected: false,
    priority: 'normal'
  }
];

export function EmailList({ 
  emails = mockEmails, 
  onEmailClick, 
  onEmailSelect, 
  onStarToggle, 
  onArchive, 
  onDelete,
  className 
}: EmailListProps) {
  const [hoveredEmail, setHoveredEmail] = React.useState<string | null>(null);

  const handleEmailClick = (email: Email) => {
    onEmailClick?.(email);
  };

  const handleSelectToggle = (emailId: string, currentSelected: boolean) => {
    onEmailSelect?.(emailId, !currentSelected);
  };

  const handleStarToggle = (e: React.MouseEvent, emailId: string) => {
    e.stopPropagation();
    onStarToggle?.(emailId);
  };

  const handleArchive = (e: React.MouseEvent, emailId: string) => {
    e.stopPropagation();
    onArchive?.(emailId);
  };

  const handleDelete = (e: React.MouseEvent, emailId: string) => {
    e.stopPropagation();
    onDelete?.(emailId);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <motion.div
      className={cn('list-container', className)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <AnimatePresence>
        {emails.map((email) => (
          <motion.div
            key={email.id}
            variants={itemVariants}
            layout
            exit={{ opacity: 0, x: -100 }}
            onMouseEnter={() => setHoveredEmail(email.id)}
            onMouseLeave={() => setHoveredEmail(null)}
          >
            <Card
              className={cn(
                'card-base cursor-pointer interactive-base interactive-hover interactive-press',
                !email.isRead && 'border-l-4 border-l-ios-blue bg-blue-50/30',
                email.isSelected && 'ring-2 ring-ios-blue bg-blue-50/50'
              )}
              padding="none"
              onClick={() => handleEmailClick(email)}
            >
              <div className="list-item">
                {/* 选择框 */}
                <motion.button
                  className="flex-shrink-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelectToggle(email.id, email.isSelected);
                  }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {email.isSelected ? (
                    <CheckCircle2 className="h-5 w-5 text-ios-blue" />
                  ) : (
                    <Circle className="h-5 w-5 text-gray-400 hover:text-ios-blue" />
                  )}
                </motion.button>

                {/* 头像 */}
                <Avatar
                  src={email.from.avatar}
                  alt={email.from.name}
                  fallback={email.from.name}
                  size="default"
                />

                {/* 邮件内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <span className={cn(
                        'font-medium truncate',
                        !email.isRead ? 'text-text-primary' : 'text-text-secondary'
                      )}>
                        {email.from.name}
                      </span>
                      {email.priority === 'high' && (
                        <Badge variant="danger" size="sm">高</Badge>
                      )}
                      {email.labels?.map((label) => (
                        <Badge key={label} variant="outline" size="sm">
                          {label}
                        </Badge>
                      ))}
                    </div>
                    <span className="text-xs text-text-tertiary flex-shrink-0">
                      {formatRelativeTime(email.timestamp)}
                    </span>
                  </div>
                  
                  <h3 className={cn(
                    'text-sm truncate mb-1',
                    !email.isRead ? 'font-semibold text-text-primary' : 'font-normal text-text-secondary'
                  )}>
                    {email.subject}
                  </h3>
                  
                  <p className="text-xs text-text-tertiary truncate">
                    {truncateText(email.preview, 100)}
                  </p>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-1 flex-shrink-0">
                  {email.hasAttachment && (
                    <Paperclip className="h-4 w-4 text-text-tertiary" />
                  )}
                  
                  <motion.button
                    onClick={(e) => handleStarToggle(e, email.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="p-1"
                  >
                    <Star 
                      className={cn(
                        'h-4 w-4',
                        email.isStarred 
                          ? 'text-ios-yellow fill-current' 
                          : 'text-text-tertiary hover:text-ios-yellow'
                      )} 
                    />
                  </motion.button>

                  {/* 悬停时显示的操作按钮 */}
                  <AnimatePresence>
                    {hoveredEmail === email.id && (
                      <motion.div
                        className="flex items-center space-x-1"
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={(e) => handleArchive(e, email.id)}
                        >
                          <Archive className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={(e) => handleDelete(e, email.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
}
