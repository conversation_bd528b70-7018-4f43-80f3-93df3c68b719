'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Mail, 
  Shield, 
  BarChart3, 
  Settings,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Header } from '@/components/layout/Header';

interface StatCard {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: string;
}

const stats: StatCard[] = [
  {
    title: '总用户数',
    value: '12,345',
    change: '+12%',
    trend: 'up',
    icon: <Users className="h-6 w-6" />,
    color: 'text-ios-blue'
  },
  {
    title: '活跃邮箱',
    value: '8,901',
    change: '+8%',
    trend: 'up',
    icon: <Mail className="h-6 w-6" />,
    color: 'text-ios-green'
  },
  {
    title: '今日邮件',
    value: '2,456',
    change: '-3%',
    trend: 'down',
    icon: <TrendingUp className="h-6 w-6" />,
    color: 'text-ios-orange'
  },
  {
    title: '存储使用',
    value: '67%',
    change: '+5%',
    trend: 'up',
    icon: <Database className="h-6 w-6" />,
    color: 'text-ios-purple'
  }
];

const recentActivities = [
  {
    id: 1,
    type: 'user_register',
    message: '新用户注册',
    user: '<EMAIL>',
    time: '2分钟前',
    status: 'success'
  },
  {
    id: 2,
    type: 'email_blocked',
    message: '垃圾邮件被拦截',
    user: '<EMAIL>',
    time: '5分钟前',
    status: 'warning'
  },
  {
    id: 3,
    type: 'system_alert',
    message: '系统负载过高',
    user: 'system',
    time: '10分钟前',
    status: 'error'
  },
  {
    id: 4,
    type: 'email_sent',
    message: '邮件发送成功',
    user: '<EMAIL>',
    time: '15分钟前',
    status: 'success'
  }
];

export default function AdminPage() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-ios-green" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-ios-orange" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-ios-red" />;
      default:
        return <Clock className="h-4 w-4 text-text-tertiary" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="success" size="sm">成功</Badge>;
      case 'warning':
        return <Badge variant="warning" size="sm">警告</Badge>;
      case 'error':
        return <Badge variant="danger" size="sm">错误</Badge>;
      default:
        return <Badge variant="default" size="sm">信息</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-surface-gray">
      {/* 头部导航 */}
      <Header onMenuClick={() => setSidebarOpen(true)} />
      
      {/* 主要内容 */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h1 className="text-3xl font-bold text-text-primary mb-2">管理控制台</h1>
          <p className="text-text-secondary">监控和管理您的临时邮箱服务</p>
        </motion.div>

        {/* 统计卡片 */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card hover className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-text-secondary mb-1">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-text-primary">
                        {stat.value}
                      </p>
                      <div className="flex items-center mt-2">
                        <span className={`text-sm font-medium ${
                          stat.trend === 'up' ? 'text-ios-green' : 
                          stat.trend === 'down' ? 'text-ios-red' : 'text-text-tertiary'
                        }`}>
                          {stat.change}
                        </span>
                        <span className="text-xs text-text-tertiary ml-1">
                          vs 上月
                        </span>
                      </div>
                    </div>
                    <div className={`${stat.color}`}>
                      {stat.icon}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 最近活动 */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <motion.div
                      key={activity.id}
                      className="flex items-center justify-between p-3 rounded-ios bg-surface-gray hover:bg-gray-100 transition-colors"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(activity.status)}
                        <div>
                          <p className="text-sm font-medium text-text-primary">
                            {activity.message}
                          </p>
                          <p className="text-xs text-text-tertiary">
                            {activity.user} • {activity.time}
                          </p>
                        </div>
                      </div>
                      {getStatusBadge(activity.status)}
                    </motion.div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <Button variant="outline" fullWidth>
                    查看所有活动
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 快速操作 */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            {/* 快速操作卡片 */}
            <Card>
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<Users className="h-4 w-4" />}
                  className="justify-start"
                >
                  用户管理
                </Button>
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<Shield className="h-4 w-4" />}
                  className="justify-start"
                >
                  安全设置
                </Button>
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<BarChart3 className="h-4 w-4" />}
                  className="justify-start"
                >
                  统计报告
                </Button>
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<Settings className="h-4 w-4" />}
                  className="justify-start"
                >
                  系统设置
                </Button>
              </CardContent>
            </Card>

            {/* 系统状态 */}
            <Card>
              <CardHeader>
                <CardTitle>系统状态</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">服务状态</span>
                  <Badge variant="success" size="sm">正常</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">数据库</span>
                  <Badge variant="success" size="sm">正常</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">邮件服务</span>
                  <Badge variant="warning" size="sm">延迟</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">存储</span>
                  <Badge variant="success" size="sm">正常</Badge>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
