'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  Star, 
  Archive, 
  Trash2, 
  Reply, 
  ReplyAll, 
  Forward,
  Download,
  MoreHorizontal,
  Paperclip,
  Shield,
  AlertTriangle
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Avatar } from '@/components/ui/Avatar';
import { formatDate, cn } from '@/lib/utils';

interface EmailDetailProps {
  email?: {
    id: string;
    from: {
      name: string;
      email: string;
      avatar?: string;
    };
    to: {
      name: string;
      email: string;
    }[];
    cc?: {
      name: string;
      email: string;
    }[];
    subject: string;
    content: string;
    timestamp: Date;
    isStarred: boolean;
    hasAttachment: boolean;
    attachments?: {
      id: string;
      name: string;
      size: string;
      type: string;
    }[];
    priority?: 'high' | 'normal' | 'low';
    isSpam?: boolean;
    isSecure?: boolean;
  };
  onBack?: () => void;
  onStarToggle?: () => void;
  onArchive?: () => void;
  onDelete?: () => void;
  onReply?: () => void;
  onReplyAll?: () => void;
  onForward?: () => void;
  className?: string;
}

const mockEmail = {
  id: '1',
  from: {
    name: 'GitHub',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40'
  },
  to: [
    { name: '用户', email: '<EMAIL>' }
  ],
  cc: [
    { name: '安全团队', email: '<EMAIL>' }
  ],
  subject: '[GitHub] 您的账户安全提醒',
  content: `
    <div>
      <p>亲爱的用户，</p>
      <br>
      <p>我们检测到您的 GitHub 账户有新的登录活动。如果这是您本人的操作，请忽略此邮件。</p>
      <br>
      <p><strong>登录详情：</strong></p>
      <ul>
        <li>时间：2024年6月26日 14:30 (UTC+8)</li>
        <li>位置：中国，北京</li>
        <li>设备：Chrome on Windows</li>
        <li>IP地址：*************</li>
      </ul>
      <br>
      <p>如果这不是您的操作，请立即：</p>
      <ol>
        <li>更改您的密码</li>
        <li>启用两步验证</li>
        <li>检查您的账户活动</li>
      </ol>
      <br>
      <p>如有任何疑问，请联系我们的支持团队。</p>
      <br>
      <p>GitHub 安全团队</p>
    </div>
  `,
  timestamp: new Date(Date.now() - 1000 * 60 * 30),
  isStarred: true,
  hasAttachment: true,
  attachments: [
    {
      id: '1',
      name: 'security-report.pdf',
      size: '245 KB',
      type: 'application/pdf'
    },
    {
      id: '2',
      name: 'login-activity.csv',
      size: '12 KB',
      type: 'text/csv'
    }
  ],
  priority: 'high',
  isSpam: false,
  isSecure: true
};

export function EmailDetail({ 
  email = mockEmail, 
  onBack, 
  onStarToggle, 
  onArchive, 
  onDelete, 
  onReply, 
  onReplyAll, 
  onForward,
  className 
}: EmailDetailProps) {
  const [showFullHeaders, setShowFullHeaders] = React.useState(false);

  const handleDownloadAttachment = (attachmentId: string) => {
    // 实现附件下载逻辑
    console.log('下载附件:', attachmentId);
  };

  return (
    <motion.div
      className={cn('h-full flex flex-col', className)}
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-surface-light">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="md:hidden"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="text-lg font-semibold text-text-primary truncate">
            {email.subject}
          </h2>
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onStarToggle}
          >
            <Star 
              className={cn(
                'h-5 w-5',
                email.isStarred 
                  ? 'text-ios-yellow fill-current' 
                  : 'text-text-tertiary hover:text-ios-yellow'
              )} 
            />
          </Button>
          <Button variant="ghost" size="icon" onClick={onArchive}>
            <Archive className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" onClick={onDelete}>
            <Trash2 className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* 邮件内容 */}
      <div className="flex-1 overflow-auto p-4 space-y-4">
        {/* 安全提示 */}
        {email.isSpam && (
          <motion.div
            className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-ios text-red-700"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <AlertTriangle className="h-5 w-5" />
            <span className="text-sm">此邮件可能是垃圾邮件，请谨慎处理。</span>
          </motion.div>
        )}

        {email.isSecure && (
          <motion.div
            className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-ios text-green-700"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Shield className="h-5 w-5" />
            <span className="text-sm">此邮件已通过安全验证。</span>
          </motion.div>
        )}

        {/* 邮件头部信息 */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <Avatar
                  src={email.from.avatar}
                  alt={email.from.name}
                  fallback={email.from.name}
                  size="lg"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-semibold text-text-primary">
                      {email.from.name}
                    </h3>
                    {email.priority === 'high' && (
                      <Badge variant="danger" size="sm">高优先级</Badge>
                    )}
                  </div>
                  <p className="text-sm text-text-secondary mb-2">
                    {email.from.email}
                  </p>
                  <div className="text-xs text-text-tertiary space-y-1">
                    <p>
                      收件人: {email.to.map(to => to.email).join(', ')}
                    </p>
                    {email.cc && email.cc.length > 0 && (
                      <p>
                        抄送: {email.cc.map(cc => cc.email).join(', ')}
                      </p>
                    )}
                    <p>{formatDate(email.timestamp)}</p>
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFullHeaders(!showFullHeaders)}
              >
                {showFullHeaders ? '隐藏' : '详情'}
              </Button>
            </div>

            <AnimatePresence>
              {showFullHeaders && (
                <motion.div
                  className="mt-4 p-3 bg-surface-gray rounded-ios text-xs text-text-secondary"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  <p><strong>Message-ID:</strong> &lt;{email.id}@github.com&gt;</p>
                  <p><strong>Date:</strong> {email.timestamp.toISOString()}</p>
                  <p><strong>Content-Type:</strong> text/html; charset=UTF-8</p>
                </motion.div>
              )}
            </AnimatePresence>
          </CardHeader>

          <CardContent>
            {/* 邮件正文 */}
            <div 
              className="prose prose-sm max-w-none text-text-primary"
              dangerouslySetInnerHTML={{ __html: email.content }}
            />

            {/* 附件 */}
            {email.hasAttachment && email.attachments && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2 mb-3">
                  <Paperclip className="h-4 w-4 text-text-secondary" />
                  <span className="text-sm font-medium text-text-secondary">
                    附件 ({email.attachments.length})
                  </span>
                </div>
                <div className="space-y-2">
                  {email.attachments.map((attachment) => (
                    <motion.div
                      key={attachment.id}
                      className="flex items-center justify-between p-3 bg-surface-gray rounded-ios"
                      whileHover={{ backgroundColor: '#e5e7eb' }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-ios-blue rounded flex items-center justify-center">
                          <Paperclip className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-text-primary">
                            {attachment.name}
                          </p>
                          <p className="text-xs text-text-tertiary">
                            {attachment.size}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownloadAttachment(attachment.id)}
                        leftIcon={<Download className="h-4 w-4" />}
                      >
                        下载
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 底部操作栏 */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-surface-light">
        <div className="flex items-center space-x-2">
          <Button
            variant="primary"
            leftIcon={<Reply className="h-4 w-4" />}
            onClick={onReply}
          >
            回复
          </Button>
          <Button
            variant="outline"
            leftIcon={<ReplyAll className="h-4 w-4" />}
            onClick={onReplyAll}
          >
            全部回复
          </Button>
          <Button
            variant="outline"
            leftIcon={<Forward className="h-4 w-4" />}
            onClick={onForward}
          >
            转发
          </Button>
        </div>
      </div>
    </motion.div>
  );
}
