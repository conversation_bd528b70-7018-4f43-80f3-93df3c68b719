'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'bg-surface-gray text-text-primary',
        primary: 'bg-ios-blue text-white',
        secondary: 'bg-gray-100 text-gray-800',
        success: 'bg-ios-green text-white',
        warning: 'bg-ios-orange text-white',
        danger: 'bg-ios-red text-white',
        outline: 'border border-gray-300 bg-transparent text-text-primary',
      },
      size: {
        sm: 'px-2 py-0.5 text-xs',
        default: 'px-2.5 py-0.5 text-xs',
        lg: 'px-3 py-1 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  animate?: boolean;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, size, animate = false, children, ...props }, ref) => {
    return (
      <motion.div
        className={cn(badgeVariants({ variant, size }), className)}
        ref={ref}
        initial={animate ? { scale: 0, opacity: 0 } : false}
        animate={animate ? { scale: 1, opacity: 1 } : {}}
        whileHover={animate ? { scale: 1.05 } : {}}
        transition={{ type: "spring", stiffness: 500, damping: 30 }}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

Badge.displayName = 'Badge';

export { Badge, badgeVariants };
