'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Inbox, 
  Send, 
  Star, 
  Settings, 
  Plus 
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

interface BottomNavProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  onNewEmail?: () => void;
  className?: string;
}

interface NavItem {
  id: string;
  icon: React.ReactNode;
  label: string;
  count?: number;
}

const navItems: NavItem[] = [
  { id: 'inbox', icon: <Inbox className="h-5 w-5" />, label: '收件箱', count: 12 },
  { id: 'sent', icon: <Send className="h-5 w-5" />, label: '已发送' },
  { id: 'starred', icon: <Star className="h-5 w-5" />, label: '星标', count: 3 },
  { id: 'settings', icon: <Settings className="h-5 w-5" />, label: '设置' },
];

export function BottomNav({ 
  activeTab = 'inbox', 
  onTabChange, 
  onNewEmail, 
  className 
}: BottomNavProps) {
  const handleTabClick = (tabId: string) => {
    onTabChange?.(tabId);
  };

  return (
    <motion.div
      className={cn(
        'fixed bottom-0 left-0 right-0 z-40 bg-surface-light/90 ios-blur border-t border-gray-200 md:hidden',
        className
      )}
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="flex items-center justify-around px-2 py-2 safe-area-inset-bottom">
        {navItems.map((item, index) => {
          const isActive = activeTab === item.id;
          
          return (
            <motion.button
              key={item.id}
              className={cn(
                'flex flex-col items-center justify-center p-2 rounded-ios min-w-0 flex-1',
                'transition-colors duration-200',
                isActive 
                  ? 'text-ios-blue' 
                  : 'text-text-tertiary hover:text-text-secondary'
              )}
              onClick={() => handleTabClick(item.id)}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="relative">
                {item.icon}
                {item.count && (
                  <Badge
                    variant={isActive ? "primary" : "default"}
                    size="sm"
                    className="absolute -top-2 -right-2 h-4 w-4 p-0 flex items-center justify-center text-xs"
                    animate
                  >
                    {item.count > 99 ? '99+' : item.count}
                  </Badge>
                )}
              </div>
              <span className={cn(
                'text-xs mt-1 truncate',
                isActive ? 'font-medium' : 'font-normal'
              )}>
                {item.label}
              </span>
              
              {/* 活跃指示器 */}
              {isActive && (
                <motion.div
                  className="absolute bottom-0 left-1/2 w-1 h-1 bg-ios-blue rounded-full"
                  layoutId="activeIndicator"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </motion.button>
          );
        })}
        
        {/* 新建邮箱按钮 */}
        <motion.div
          className="flex-shrink-0 ml-2"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
        >
          <Button
            variant="primary"
            size="icon"
            onClick={onNewEmail}
            className="h-12 w-12 rounded-full shadow-lg"
          >
            <Plus className="h-5 w-5" />
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
}
