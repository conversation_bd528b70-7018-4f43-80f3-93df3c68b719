'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Mail, Menu, Settings, User, Bell, Search } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Avatar } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { LoginModal } from '@/components/auth/LoginModal';
import { cn } from '@/lib/utils';

interface HeaderProps {
  onMenuClick?: () => void;
  className?: string;
}

export function Header({ onMenuClick, className }: HeaderProps) {
  const [isSearchFocused, setIsSearchFocused] = React.useState(false);
  const [showLoginModal, setShowLoginModal] = React.useState(false);
  const [isLoggedIn, setIsLoggedIn] = React.useState(false);

  return (
    <motion.header
      className={cn(
        'sticky top-0 z-50 w-full border-b border-gray-200 bg-surface-light/80 ios-blur',
        className
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="container-main">
        <div className="nav-container">
          {/* 左侧 - Logo 和菜单 */}
          <div className="nav-brand">
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="mobile-only"
            >
              <Menu className="h-5 w-5" />
            </Button>

            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <div className="flex h-8 w-8 items-center justify-center rounded-ios bg-ios-blue">
                <Mail className="h-5 w-5 text-white" />
              </div>
              <span className="tablet-up text-title">
                临时邮箱
              </span>
            </motion.div>
          </div>

          {/* 中间 - 搜索框 */}
          <div className="desktop-only flex-1 max-w-md mx-8">
            <motion.div
              className="w-full"
              animate={{
                scale: isSearchFocused ? 1.02 : 1,
              }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <Input
                type="search"
                placeholder="搜索邮件..."
                leftIcon={<Search className="h-4 w-4" />}
                variant="filled"
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                className="w-full"
              />
            </motion.div>
          </div>

          {/* 右侧 - 操作按钮 */}
          <div className="nav-actions">
            {/* 搜索按钮 (移动端) */}
            <Button
              variant="ghost"
              size="icon"
              className="mobile-only"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* 通知 */}
            <motion.div
              className="relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <Badge
                variant="danger"
                size="sm"
                className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center"
                animate
              >
                3
              </Badge>
            </motion.div>

            {/* 设置 */}
            <Button variant="ghost" size="icon">
              <Settings className="h-5 w-5" />
            </Button>

            {/* 用户头像或登录按钮 */}
            {isLoggedIn ? (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Avatar
                  src="/api/placeholder/32/32"
                  alt="用户头像"
                  fallback="用户"
                  online={true}
                  className="cursor-pointer"
                />
              </motion.div>
            ) : (
              <Button
                variant="primary"
                size="sm"
                onClick={() => setShowLoginModal(true)}
                leftIcon={<User className="h-4 w-4" />}
              >
                登录
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLogin={(credentials) => {
          console.log('登录:', credentials);
          setIsLoggedIn(true);
          setShowLoginModal(false);
        }}
        onOAuthLogin={(provider) => {
          console.log('OAuth登录:', provider);
          setIsLoggedIn(true);
          setShowLoginModal(false);
        }}
        onPasskeyLogin={() => {
          console.log('Passkey登录');
          setIsLoggedIn(true);
          setShowLoginModal(false);
        }}
      />
    </motion.header>
  );
}
