'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/Header';
import { Sidebar } from '@/components/layout/Sidebar';
import { BottomNav } from '@/components/layout/BottomNav';
import { EmailList } from '@/components/email/EmailList';
import { EmailDetail } from '@/components/email/EmailDetail';
import { CreateEmailModal } from '@/components/email/CreateEmailModal';

export default function Home() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const [selectedEmail, setSelectedEmail] = React.useState<any>(null);
  const [isMobile, setIsMobile] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState('inbox');
  const [showCreateModal, setShowCreateModal] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleEmailClick = (email: any) => {
    setSelectedEmail(email);
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const handleBackToList = () => {
    setSelectedEmail(null);
  };

  const handleNewEmail = () => {
    setShowCreateModal(true);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSelectedEmail(null); // 切换标签时清除选中的邮件
  };

  return (
    <div className="h-screen flex flex-col bg-surface-gray pb-safe md:pb-0">
      {/* 头部导航 */}
      <Header onMenuClick={() => setSidebarOpen(true)} />

      {/* 主要内容区域 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar
          isOpen={sidebarOpen || !isMobile}
          onClose={() => setSidebarOpen(false)}
        />

        {/* 邮件列表和详情 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 邮件列表 */}
          <motion.div
            className={`
              ${isMobile ? 'w-full' : 'w-1/3 border-r border-gray-200'}
              ${selectedEmail && isMobile ? 'hidden' : 'block'}
              bg-surface-light overflow-auto
            `}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="p-4">
              <h2 className="text-lg font-semibold text-text-primary mb-4">
                收件箱
              </h2>
              <EmailList onEmailClick={handleEmailClick} />
            </div>
          </motion.div>

          {/* 邮件详情 */}
          <motion.div
            className={`
              ${isMobile ? 'w-full' : 'flex-1'}
              ${!selectedEmail && isMobile ? 'hidden' : 'block'}
              bg-surface-light
            `}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {selectedEmail ? (
              <EmailDetail
                email={selectedEmail}
                onBack={handleBackToList}
              />
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <motion.div
                    className="w-16 h-16 bg-surface-gray rounded-full flex items-center justify-center mx-auto mb-4"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  >
                    📧
                  </motion.div>
                  <h3 className="text-lg font-medium text-text-primary mb-2">
                    选择一封邮件查看
                  </h3>
                  <p className="text-text-secondary">
                    从左侧列表中选择一封邮件来查看详细内容
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>

      {/* 移动端底部导航 */}
      <BottomNav
        activeTab={activeTab}
        onTabChange={handleTabChange}
        onNewEmail={handleNewEmail}
      />

      {/* 新建邮箱模态框 */}
      <CreateEmailModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onEmailCreated={(email) => {
          console.log('新邮箱创建:', email);
          setShowCreateModal(false);
        }}
      />
    </div>
  );
}
