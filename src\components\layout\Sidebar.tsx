'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Inbox, 
  Send, 
  Archive, 
  Trash2, 
  Star, 
  Plus,
  Settings,
  Shield,
  Users,
  BarChart3,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { CreateEmailModal } from '@/components/email/CreateEmailModal';
import { cn } from '@/lib/utils';

interface SidebarProps {
  isOpen: boolean;
  onClose?: () => void;
  className?: string;
}

interface NavItem {
  icon: React.ReactNode;
  label: string;
  count?: number;
  active?: boolean;
  href?: string;
}

const mainNavItems: NavItem[] = [
  { icon: <Inbox className="h-5 w-5" />, label: '收件箱', count: 12, active: true },
  { icon: <Send className="h-5 w-5" />, label: '已发送' },
  { icon: <Star className="h-5 w-5" />, label: '星标邮件', count: 3 },
  { icon: <Archive className="h-5 w-5" />, label: '归档' },
  { icon: <Trash2 className="h-5 w-5" />, label: '垃圾箱', count: 5 },
];

const adminNavItems: NavItem[] = [
  { icon: <Users className="h-5 w-5" />, label: '用户管理' },
  { icon: <Shield className="h-5 w-5" />, label: '安全设置' },
  { icon: <BarChart3 className="h-5 w-5" />, label: '统计分析' },
  { icon: <Settings className="h-5 w-5" />, label: '系统设置' },
];

export function Sidebar({ isOpen, onClose, className }: SidebarProps) {
  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const overlayVariants = {
    open: { opacity: 1 },
    closed: { opacity: 0 }
  };

  const itemVariants = {
    open: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.1,
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }),
    closed: {
      opacity: 0,
      x: -20
    }
  };

  return (
    <>
      {/* 移动端遮罩 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-40 bg-black/50 md:hidden"
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* 侧边栏 */}
      <motion.aside
        className={cn(
          'fixed left-0 top-0 z-50 h-full w-64 bg-surface-light border-r border-gray-200 md:relative md:translate-x-0',
          className
        )}
        variants={sidebarVariants}
        initial="closed"
        animate={isOpen ? "open" : "closed"}
      >
        <div className="flex h-full flex-col">
          {/* 头部 */}
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-text-primary">邮箱管理</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="md:hidden"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* 新建邮件按钮 */}
          <div className="p-4">
            <Button
              variant="primary"
              fullWidth
              leftIcon={<Plus className="h-4 w-4" />}
              className="justify-start"
            >
              新建邮箱
            </Button>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 space-y-1 px-2">
            {/* 主要功能 */}
            <div className="space-y-1">
              {mainNavItems.map((item, index) => (
                <motion.div
                  key={item.label}
                  custom={index}
                  variants={itemVariants}
                  initial="closed"
                  animate="open"
                >
                  <Button
                    variant={item.active ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start",
                      item.active && "bg-ios-blue/10 text-ios-blue hover:bg-ios-blue/20"
                    )}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span className="flex-1 text-left">{item.label}</span>
                    {item.count && (
                      <Badge
                        variant={item.active ? "primary" : "default"}
                        size="sm"
                        animate
                      >
                        {item.count}
                      </Badge>
                    )}
                  </Button>
                </motion.div>
              ))}
            </div>

            {/* 分隔线 */}
            <div className="my-4 border-t border-gray-200" />

            {/* 管理功能 */}
            <div className="space-y-1">
              <h3 className="px-3 py-2 text-xs font-semibold text-text-secondary uppercase tracking-wider">
                管理功能
              </h3>
              {adminNavItems.map((item, index) => (
                <motion.div
                  key={item.label}
                  custom={index + mainNavItems.length}
                  variants={itemVariants}
                  initial="closed"
                  animate="open"
                >
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span className="flex-1 text-left">{item.label}</span>
                  </Button>
                </motion.div>
              ))}
            </div>
          </nav>

          {/* 底部信息 */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-text-secondary">
              <p>存储空间: 2.1GB / 15GB</p>
              <div className="mt-2 h-1 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-ios-blue rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "14%" }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
            </div>
          </div>
        </div>
      </motion.aside>
    </>
  );
}
