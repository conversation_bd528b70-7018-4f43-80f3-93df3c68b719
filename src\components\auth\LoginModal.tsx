'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Github, 
  Chrome,
  Fingerprint,
  ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin?: (credentials: { email: string; password: string }) => void;
  onOAuthLogin?: (provider: string) => void;
  onPasskeyLogin?: () => void;
}

export function LoginModal({ 
  isOpen, 
  onClose, 
  onLogin, 
  onOAuthLogin, 
  onPasskeyLogin 
}: LoginModalProps) {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [showPassword, setShowPassword] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [loginMode, setLoginMode] = React.useState<'email' | 'oauth' | 'passkey'>('email');
  const [errors, setErrors] = React.useState<{ email?: string; password?: string }>({});

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};
    
    if (!email) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }
    
    if (!password) {
      newErrors.password = '请输入密码';
    } else if (password.length < 6) {
      newErrors.password = '密码至少需要6个字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      onLogin?.({ email, password });
      onClose();
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOAuthLogin = async (provider: string) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      onOAuthLogin?.(provider);
      onClose();
    } catch (error) {
      console.error('OAuth登录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasskeyLogin = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      onPasskeyLogin?.();
      onClose();
    } catch (error) {
      console.error('Passkey登录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 50
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {/* 背景遮罩 */}
          <motion.div
            className="absolute inset-0 bg-black/50 ios-blur"
            onClick={onClose}
          />
          
          {/* 模态框内容 */}
          <motion.div
            className="relative w-full max-w-md"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <Card className="w-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-ios-blue rounded-ios flex items-center justify-center">
                      <Mail className="h-4 w-4 text-white" />
                    </div>
                    <span>登录账户</span>
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onClose}
                    className="h-8 w-8"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* 登录方式选择 */}
                <div className="flex space-x-2">
                  <Button
                    variant={loginMode === 'email' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setLoginMode('email')}
                    className="flex-1"
                  >
                    邮箱登录
                  </Button>
                  <Button
                    variant={loginMode === 'oauth' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setLoginMode('oauth')}
                    className="flex-1"
                  >
                    第三方登录
                  </Button>
                  <Button
                    variant={loginMode === 'passkey' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setLoginMode('passkey')}
                    className="flex-1"
                  >
                    Passkey
                  </Button>
                </div>

                {/* 邮箱登录 */}
                {loginMode === 'email' && (
                  <motion.form
                    onSubmit={handleEmailLogin}
                    className="space-y-4"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Input
                      type="email"
                      label="邮箱地址"
                      placeholder="输入您的邮箱地址"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      error={errors.email}
                      leftIcon={<Mail className="h-4 w-4" />}
                      variant="filled"
                    />
                    
                    <Input
                      type={showPassword ? 'text' : 'password'}
                      label="密码"
                      placeholder="输入您的密码"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      error={errors.password}
                      leftIcon={<Lock className="h-4 w-4" />}
                      rightIcon={
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-text-tertiary hover:text-text-secondary"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      }
                      variant="filled"
                    />
                    
                    <Button
                      type="submit"
                      variant="primary"
                      fullWidth
                      loading={isLoading}
                      rightIcon={<ArrowRight className="h-4 w-4" />}
                    >
                      登录
                    </Button>
                  </motion.form>
                )}

                {/* OAuth登录 */}
                {loginMode === 'oauth' && (
                  <motion.div
                    className="space-y-3"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Button
                      variant="outline"
                      fullWidth
                      leftIcon={<Github className="h-4 w-4" />}
                      onClick={() => handleOAuthLogin('github')}
                      loading={isLoading}
                    >
                      使用 GitHub 登录
                    </Button>
                    
                    <Button
                      variant="outline"
                      fullWidth
                      leftIcon={<Chrome className="h-4 w-4" />}
                      onClick={() => handleOAuthLogin('google')}
                      loading={isLoading}
                    >
                      使用 Google 登录
                    </Button>
                  </motion.div>
                )}

                {/* Passkey登录 */}
                {loginMode === 'passkey' && (
                  <motion.div
                    className="space-y-4 text-center"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className="w-16 h-16 bg-ios-blue/10 rounded-full flex items-center justify-center">
                        <Fingerprint className="h-8 w-8 text-ios-blue" />
                      </div>
                      <div>
                        <h3 className="font-medium text-text-primary">使用 Passkey 登录</h3>
                        <p className="text-sm text-text-secondary mt-1">
                          使用您的生物识别或安全密钥进行安全登录
                        </p>
                      </div>
                    </div>
                    
                    <Button
                      variant="primary"
                      fullWidth
                      onClick={handlePasskeyLogin}
                      loading={isLoading}
                      leftIcon={<Fingerprint className="h-4 w-4" />}
                    >
                      使用 Passkey 登录
                    </Button>
                  </motion.div>
                )}

                {/* 提示信息 */}
                <div className="text-center">
                  <p className="text-xs text-text-tertiary">
                    登录即表示您同意我们的
                    <a href="#" className="text-ios-blue hover:underline">服务条款</a>
                    和
                    <a href="#" className="text-ios-blue hover:underline">隐私政策</a>
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
