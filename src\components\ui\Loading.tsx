'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'default' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars';
  className?: string;
  text?: string;
}

export function Loading({ 
  size = 'default', 
  variant = 'spinner', 
  className, 
  text 
}: LoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    default: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const renderSpinner = () => (
    <motion.div
      className={cn(
        'border-2 border-gray-200 border-t-ios-blue rounded-full',
        sizeClasses[size],
        className
      )}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }}
    />
  );

  const renderDots = () => (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={cn(
            'bg-ios-blue rounded-full',
            size === 'sm' ? 'w-1 h-1' : size === 'lg' ? 'w-3 h-3' : 'w-2 h-2'
          )}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: index * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <motion.div
      className={cn(
        'bg-ios-blue rounded-full',
        sizeClasses[size],
        className
      )}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.5, 1, 0.5],
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );

  const renderBars = () => (
    <div className={cn('flex items-end space-x-1', className)}>
      {[0, 1, 2, 3].map((index) => (
        <motion.div
          key={index}
          className={cn(
            'bg-ios-blue rounded-sm',
            size === 'sm' ? 'w-0.5' : size === 'lg' ? 'w-1.5' : 'w-1',
            size === 'sm' ? 'h-2' : size === 'lg' ? 'h-6' : 'h-4'
          )}
          animate={{
            scaleY: [1, 2, 1],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );

  const renderLoading = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'bars':
        return renderBars();
      default:
        return renderSpinner();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      {renderLoading()}
      {text && (
        <motion.p
          className="text-sm text-text-secondary"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
}

// 全屏加载组件
interface FullScreenLoadingProps {
  isLoading: boolean;
  text?: string;
  variant?: LoadingProps['variant'];
}

export function FullScreenLoading({ 
  isLoading, 
  text = '加载中...', 
  variant = 'spinner' 
}: FullScreenLoadingProps) {
  if (!isLoading) return null;

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 ios-blur"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-surface-light rounded-ios-lg p-8 shadow-ios-xl"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <Loading size="lg" variant={variant} text={text} />
      </motion.div>
    </motion.div>
  );
}

// 页面加载骨架屏
export function EmailListSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(5)].map((_, index) => (
        <motion.div
          key={index}
          className="p-4 bg-surface-light rounded-ios border border-gray-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <div className="flex items-center space-x-3">
            {/* 头像骨架 */}
            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
            
            {/* 内容骨架 */}
            <div className="flex-1 space-y-2">
              <div className="flex items-center justify-between">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-24" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-16" />
              </div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-48" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-64" />
            </div>
            
            {/* 操作按钮骨架 */}
            <div className="flex space-x-2">
              <div className="w-6 h-6 bg-gray-200 rounded animate-pulse" />
              <div className="w-6 h-6 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

// 邮件详情骨架屏
export function EmailDetailSkeleton() {
  return (
    <motion.div
      className="h-full p-6 space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {/* 头部骨架 */}
      <div className="flex items-center justify-between">
        <div className="h-6 bg-gray-200 rounded animate-pulse w-64" />
        <div className="flex space-x-2">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
      
      {/* 发件人信息骨架 */}
      <div className="flex items-start space-x-4 p-4 bg-surface-gray rounded-ios">
        <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
          <div className="h-3 bg-gray-200 rounded animate-pulse w-48" />
          <div className="h-3 bg-gray-200 rounded animate-pulse w-40" />
        </div>
      </div>
      
      {/* 邮件内容骨架 */}
      <div className="space-y-3">
        {[...Array(8)].map((_, index) => (
          <div
            key={index}
            className={cn(
              'h-4 bg-gray-200 rounded animate-pulse',
              index === 3 ? 'w-3/4' : index === 7 ? 'w-1/2' : 'w-full'
            )}
          />
        ))}
      </div>
      
      {/* 附件骨架 */}
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
        <div className="flex items-center space-x-3 p-3 bg-surface-gray rounded-ios">
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
          <div className="flex-1 space-y-1">
            <div className="h-3 bg-gray-200 rounded animate-pulse w-32" />
            <div className="h-2 bg-gray-200 rounded animate-pulse w-16" />
          </div>
          <div className="w-16 h-6 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    </motion.div>
  );
}
