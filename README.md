# 临时邮箱 - Cloudflare 临时邮箱工具平台

一个现代化的临时邮箱服务平台，采用 iOS 风格设计，支持响应式布局，适配桌面、平板和移动设备。

## ✨ 特性

### 📧 邮件处理
- 🚀 使用 Rust WASM 解析邮件，解析速度快，兼容性强
- ✅ 支持发送邮件，支持 DKIM 验证
- 📤 支持 SMTP 和 Resend 等多种发送方式
- 📎 支持查看附件，附件图片显示
- 💾 支持 S3 附件存储和删除功能
- 🛡️ 垃圾邮件检测和黑白名单配置
- 📨 邮件转发功能，支持全局转发地址

### 👥 用户管理
- 🔑 使用凭证重新登录之前的邮箱
- 📝 完整的用户注册登录功能，可绑定邮箱地址
- 🔐 支持 OAuth2 第三方登录（Github、Google 等）
- 🔒 支持 Passkey 无密码登录
- 👤 用户角色管理，支持多角色域名和前缀配置
- 📋 用户收件箱查看，支持地址和关键词过滤

### 🔧 管理功能
- 🎛️ 完整的 admin 控制台
- ✉️ admin 后台创建无前缀邮箱
- 👥 admin 用户管理页面，增加用户地址查看功能
- 🧹 定时清理功能，支持多种清理策略
- 🎯 获取自定义名字的邮箱，admin 可配置黑名单
- 🔐 增加访问密码，可作为私人站点

### 🌐 多语言与界面
- 🌍 前后台均支持多语言（中文/英文）
- 🎨 现代化 iOS 风格 UI 设计
- 📱 支持响应式布局，适配所有设备
- 🎭 支持深色/浅色主题切换
- 🔒 使用 shadow DOM 防止样式污染
- 🔗 支持 URL JWT 参数自动登录

### 🤖 集成与扩展
- 🤖 完整的 Telegram Bot 支持，以及 Telegram 推送
- 📧 添加 SMTP proxy server，支持 SMTP 发送邮件，IMAP 查看邮件
- 🔗 Webhook 支持，消息推送集成
- 🛡️ 支持 CF Turnstile 人机验证
- ⚡ 限流配置，防止滥用

## 🛠️ 技术栈

- **前端框架**: Next.js 15 + React 18
- **样式**: Tailwind CSS + iOS 风格设计系统
- **动画**: Framer Motion
- **UI 组件**: 自定义 iOS 风格组件库
- **图标**: Lucide React
- **类型安全**: TypeScript
- **国际化**: next-intl
- **状态管理**: React Hooks
- **响应式**: 移动优先设计

## 🎨 设计特色

### iOS 风格设计
- 🎯 遵循 Apple Human Interface Guidelines
- 🎨 iOS 系统颜色方案（蓝色、绿色、红色等）
- 📐 iOS 风格圆角和阴影
- 🔤 SF Pro Display 字体系列
- 🎭 毛玻璃效果和模糊背景

### 响应式布局
- 📱 移动端：底部导航栏 + 全屏模态框
- 💻 桌面端：侧边栏 + 三栏布局
- 📟 平板端：自适应布局切换
- 🔄 流畅的设备间切换体验

### 动画效果
- 🌊 流畅的页面转场动画
- 🎯 精细的交互反馈
- 📱 iOS 风格的弹簧动画
- ⚡ 性能优化的动画实现

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn 或 pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 开发模式
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
npm start
```

## 📱 PWA 支持

应用支持 PWA（Progressive Web App）功能：
- 📱 可安装到主屏幕
- 🔄 离线缓存支持
- 📲 原生应用体验
- 🚀 快速启动

## 🌍 多语言支持

目前支持的语言：
- 🇨🇳 简体中文 (zh)
- 🇺🇸 English (en)

语言文件位置：`src/i18n/locales/`

## 📂 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # 管理后台页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── auth/             # 认证相关组件
│   ├── email/            # 邮件相关组件
│   ├── layout/           # 布局组件
│   └── ui/               # UI 基础组件
├── i18n/                 # 国际化配置
│   ├── locales/          # 语言文件
│   └── config.ts         # i18n 配置
└── lib/                  # 工具函数
    └── utils.ts          # 通用工具
```

## 🎯 核心功能

### 邮件管理
- ✉️ 创建临时邮箱（支持自定义前缀）
- 📥 实时接收邮件
- 📧 邮件详情查看
- 📎 附件下载
- ⭐ 邮件标星
- 🗂️ 邮件归档
- 🗑️ 邮件删除

### 用户体验
- 🔐 多种登录方式
- 🎨 主题切换
- 🌍 语言切换
- 📱 响应式设计
- ⚡ 快速加载
- 🎭 流畅动画

### 管理后台
- 📊 数据统计
- 👥 用户管理
- 🛡️ 安全设置
- 📈 使用分析
- ⚙️ 系统配置

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Framer Motion](https://www.framer.com/motion/) - 动画库
- [Lucide](https://lucide.dev/) - 图标库
- [Apple Design](https://developer.apple.com/design/) - 设计灵感

---

🚀 **开始使用临时邮箱，享受安全便捷的邮件服务！**
