'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Copy, RefreshCw, Mail, Check } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { generateRandomEmail, copyToClipboard, cn } from '@/lib/utils';

interface CreateEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEmailCreated?: (email: string) => void;
}

export function CreateEmailModal({ isOpen, onClose, onEmailCreated }: CreateEmailModalProps) {
  const [customPrefix, setCustomPrefix] = React.useState('');
  const [selectedDomain, setSelectedDomain] = React.useState('temp-mail.io');
  const [generatedEmail, setGeneratedEmail] = React.useState('');
  const [copied, setCopied] = React.useState(false);
  const [isCreating, setIsCreating] = React.useState(false);

  const domains = [
    'temp-mail.io',
    'tempmail.plus',
    'guerrillamail.com',
    '10minutemail.com',
    'mailinator.com'
  ];

  React.useEffect(() => {
    if (isOpen) {
      generateNewEmail();
    }
  }, [isOpen, selectedDomain]);

  const generateNewEmail = () => {
    const email = customPrefix 
      ? `${customPrefix}@${selectedDomain}`
      : generateRandomEmail(selectedDomain);
    setGeneratedEmail(email);
  };

  const handleCopy = async () => {
    const success = await copyToClipboard(generatedEmail);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleCreateEmail = async () => {
    setIsCreating(true);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    onEmailCreated?.(generatedEmail);
    setIsCreating(false);
    onClose();
  };

  const handleCustomPrefixChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9]/g, '');
    setCustomPrefix(value);
    if (value) {
      setGeneratedEmail(`${value}@${selectedDomain}`);
    } else {
      generateNewEmail();
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 50
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="modal-overlay"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {/* 背景遮罩 */}
          <motion.div
            className="absolute inset-0 bg-black/50 ios-blur"
            onClick={onClose}
          />

          {/* 模态框内容 */}
          <motion.div
            className="modal-container"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <Card className="w-full card-base">
              <CardHeader className="modal-header">
                  <CardTitle className="flex items-center space-x-2">
                    <Mail className="h-5 w-5 text-ios-blue" />
                    <span>创建临时邮箱</span>
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onClose}
                    className="h-8 w-8"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="modal-content form-container">
                {/* 自定义前缀 */}
                <div>
                  <Input
                    label="自定义前缀 (可选)"
                    placeholder="输入自定义前缀"
                    value={customPrefix}
                    onChange={handleCustomPrefixChange}
                    variant="filled"
                  />
                  <p className="text-xs text-text-tertiary mt-1">
                    留空将自动生成随机前缀
                  </p>
                </div>

                {/* 域名选择 */}
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    选择域名
                  </label>
                  <div className="grid grid-cols-1 gap-2">
                    {domains.map((domain) => (
                      <motion.button
                        key={domain}
                        className={cn(
                          'p-3 rounded-ios border text-left transition-all duration-200',
                          selectedDomain === domain
                            ? 'border-ios-blue bg-ios-blue/10 text-ios-blue'
                            : 'border-gray-200 hover:border-gray-300 text-text-primary'
                        )}
                        onClick={() => setSelectedDomain(domain)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{domain}</span>
                          {selectedDomain === domain && (
                            <Badge variant="primary" size="sm">已选择</Badge>
                          )}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* 生成的邮箱地址 */}
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    生成的邮箱地址
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 p-3 bg-surface-gray rounded-ios border">
                      <p className="text-sm font-mono text-text-primary break-all">
                        {generatedEmail}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={generateNewEmail}
                      disabled={!!customPrefix}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={handleCopy}
                    >
                      {copied ? (
                        <Check className="h-4 w-4 text-ios-green" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex space-x-3 pt-4">
                  <Button
                    variant="outline"
                    fullWidth
                    onClick={onClose}
                  >
                    取消
                  </Button>
                  <Button
                    variant="primary"
                    fullWidth
                    onClick={handleCreateEmail}
                    loading={isCreating}
                  >
                    创建邮箱
                  </Button>
                </div>

                {/* 提示信息 */}
                <div className="p-3 bg-blue-50 rounded-ios border border-blue-200">
                  <p className="text-xs text-blue-700">
                    💡 临时邮箱将在24小时后自动删除，请及时查收重要邮件。
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
