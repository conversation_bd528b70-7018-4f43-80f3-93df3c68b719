/* 设计系统 - 统一的布局、间距、配色规范 */

/* ===== 布局容器规范 ===== */
.container-main {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-content {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-narrow {
  @apply max-w-2xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* ===== 间距系统 ===== */
.spacing-section {
  @apply py-8 md:py-12 lg:py-16;
}

.spacing-component {
  @apply p-4 md:p-6 lg:p-8;
}

.spacing-card {
  @apply p-4 md:p-6;
}

.spacing-list-item {
  @apply py-3 px-4;
}

.spacing-form {
  @apply space-y-4 md:space-y-6;
}

.spacing-button-group {
  @apply space-x-2 md:space-x-3;
}

.spacing-stack {
  @apply space-y-3 md:space-y-4;
}

.spacing-grid {
  @apply gap-4 md:gap-6 lg:gap-8;
}

/* ===== 网格系统 ===== */
.grid-main {
  @apply grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8;
}

.grid-sidebar {
  @apply lg:col-span-3;
}

.grid-content {
  @apply lg:col-span-6;
}

.grid-aside {
  @apply lg:col-span-3;
}

.grid-two-col {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6;
}

.grid-three-col {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6;
}

.grid-four-col {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6;
}

/* ===== 卡片布局规范 ===== */
.card-base {
  @apply bg-surface-light rounded-ios-lg border border-gray-200 shadow-ios;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-content {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-surface-gray;
}

.card-compact {
  @apply p-4;
}

.card-spacious {
  @apply p-6 md:p-8;
}

/* ===== 列表布局规范 ===== */
.list-container {
  @apply space-y-1;
}

.list-item {
  @apply flex items-center justify-between py-3 px-4 hover:bg-surface-gray transition-colors duration-200;
}

.list-item-compact {
  @apply flex items-center justify-between py-2 px-3 text-sm;
}

.list-item-spacious {
  @apply flex items-center justify-between py-4 px-6;
}

.list-divider {
  @apply border-t border-gray-200 my-2;
}

/* ===== 表单布局规范 ===== */
.form-container {
  @apply space-y-6;
}

.form-group {
  @apply space-y-2;
}

.form-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-actions {
  @apply flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200;
}

/* ===== 导航布局规范 ===== */
.nav-container {
  @apply flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8;
}

.nav-brand {
  @apply flex items-center space-x-3;
}

.nav-menu {
  @apply hidden md:flex items-center space-x-6;
}

.nav-actions {
  @apply flex items-center space-x-2;
}

/* ===== 侧边栏布局规范 ===== */
.sidebar-container {
  @apply w-64 h-full bg-surface-light border-r border-gray-200;
}

.sidebar-header {
  @apply h-16 flex items-center justify-between px-4 border-b border-gray-200;
}

.sidebar-content {
  @apply flex-1 overflow-y-auto py-4;
}

.sidebar-footer {
  @apply p-4 border-t border-gray-200;
}

.sidebar-nav {
  @apply space-y-1 px-2;
}

.sidebar-nav-item {
  @apply flex items-center px-3 py-2 rounded-ios text-sm font-medium transition-colors duration-200;
}

.sidebar-nav-item-active {
  @apply bg-ios-blue text-white;
}

.sidebar-nav-item-inactive {
  @apply text-text-secondary hover:bg-surface-gray hover:text-text-primary;
}

/* ===== 内容区域布局规范 ===== */
.content-header {
  @apply flex items-center justify-between mb-6 pb-4 border-b border-gray-200;
}

.content-title {
  @apply text-2xl md:text-3xl font-bold text-text-primary;
}

.content-subtitle {
  @apply text-text-secondary mt-1;
}

.content-actions {
  @apply flex items-center space-x-3;
}

.content-body {
  @apply space-y-6;
}

/* ===== 模态框布局规范 ===== */
.modal-overlay {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 ios-blur;
}

.modal-container {
  @apply relative w-full max-w-md bg-surface-light rounded-ios-lg shadow-ios-xl;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-content {
  @apply p-6;
}

.modal-footer {
  @apply flex justify-end space-x-3 p-6 border-t border-gray-200 bg-surface-gray;
}

/* ===== 状态指示器 ===== */
.status-online {
  @apply bg-ios-green;
}

.status-offline {
  @apply bg-gray-400;
}

.status-warning {
  @apply bg-ios-orange;
}

.status-error {
  @apply bg-ios-red;
}

/* ===== 优先级指示器 ===== */
.priority-high {
  @apply text-ios-red border-ios-red bg-red-50;
}

.priority-normal {
  @apply text-text-secondary border-gray-300 bg-gray-50;
}

.priority-low {
  @apply text-text-tertiary border-gray-200 bg-gray-25;
}

/* ===== 响应式工具类 ===== */
.mobile-only {
  @apply block md:hidden;
}

.desktop-only {
  @apply hidden md:block;
}

.tablet-up {
  @apply hidden md:block;
}

.mobile-tablet {
  @apply block lg:hidden;
}

/* ===== 文本层次规范 ===== */
.text-display {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight;
}

.text-headline {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold;
}

.text-title {
  @apply text-xl md:text-2xl font-semibold;
}

.text-subtitle {
  @apply text-lg md:text-xl font-medium;
}

.text-body {
  @apply text-base leading-relaxed;
}

.text-caption {
  @apply text-sm text-text-secondary;
}

.text-footnote {
  @apply text-xs text-text-tertiary;
}

/* ===== 交互状态规范 ===== */
.interactive-base {
  @apply transition-all duration-200 ease-out;
}

.interactive-hover {
  @apply hover:scale-105 hover:shadow-lg;
}

.interactive-press {
  @apply active:scale-95;
}

.interactive-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-ios-blue focus:ring-offset-2;
}

/* ===== 加载状态规范 ===== */
.loading-container {
  @apply flex items-center justify-center py-8;
}

.loading-overlay {
  @apply absolute inset-0 bg-surface-light/80 flex items-center justify-center;
}

.skeleton-base {
  @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
  @apply h-4 bg-gray-200 rounded;
}

.skeleton-avatar {
  @apply w-10 h-10 bg-gray-200 rounded-full;
}

/* ===== 错误状态规范 ===== */
.error-container {
  @apply text-center py-8;
}

.error-icon {
  @apply w-12 h-12 text-ios-red mx-auto mb-4;
}

.error-title {
  @apply text-lg font-semibold text-text-primary mb-2;
}

.error-message {
  @apply text-text-secondary;
}

/* ===== 空状态规范 ===== */
.empty-container {
  @apply text-center py-12;
}

.empty-icon {
  @apply w-16 h-16 text-text-tertiary mx-auto mb-4;
}

.empty-title {
  @apply text-lg font-medium text-text-primary mb-2;
}

.empty-message {
  @apply text-text-secondary;
}
